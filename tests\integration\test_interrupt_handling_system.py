"""
Integration Tests for Interrupt Handling System

This test suite validates the interaction between StateManager, TTSState, and InterruptState
for comprehensive interrupt handling during TTS playback.

Test Coverage:
- Interrupt detection and confirmation
- Action reversibility analysis (reversible, irreversible, unknown)
- Context-aware acknowledgment generation
- Memory context storage and StateManager coordination
- Complete interrupt flow: detection → confirmation → acknowledgment → resume/queue
- Error handling and edge cases
"""

import sys
import asyncio
import os
import pytest
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from dotenv import load_dotenv

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

load_dotenv()

from core.state_manager.state_manager import StateManager
from core.state_manager.state_output import TTSState, InterruptState
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.memory.redis_context import RedisClient
from core.memory.memory_manager import MemoryManager
from core.config.interrupt_config import InterruptConfig, InterruptDetectionConfig
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.layer2_schema import TTSInputSchema, TTSOutputSchema

logger = get_module_logger("test_interrupt_handling", session_id="test_interrupt")


class TestInterruptHandlingSystem:
    """Test suite for interrupt handling system integration."""
    
    @pytest.fixture
    async def setup_test_environment(self):
        """Setup test environment with StateManager and mock components."""
        session_id = "test_interrupt_session"
        user_id = "test_user"
        
        # Setup logging
        setup_development_logging()
        
        # Create StateManager
        state_manager = await StateManager.create("banking_workflow.json", session_id, user_id)
        
        # Create interrupt configuration
        interrupt_config = InterruptConfig(
            detection=InterruptDetectionConfig(
                enabled=True,
                vad_threshold=0.01,
                confirmation_window_seconds=0.5,
                min_interrupt_duration_seconds=0.3
            )
        )
        
        yield {
            "state_manager": state_manager,
            "session_id": session_id,
            "user_id": user_id,
            "interrupt_config": interrupt_config
        }
        
        # Cleanup
        cleanup_logger()

    @pytest.mark.asyncio
    async def test_interrupt_state_creation_and_basic_functionality(self, setup_test_environment):
        """Test InterruptState creation and basic functionality."""
        env = await setup_test_environment
        
        # Create InterruptState
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )
        
        assert interrupt_state is not None
        assert interrupt_state.vad_threshold == 0.01
        assert interrupt_state.confirmation_window == 0.5
        assert interrupt_state.min_interrupt_duration == 0.3
        assert "reversible" in interrupt_state.interrupt_messages
        assert "irreversible" in interrupt_state.interrupt_messages
        assert "unknown" in interrupt_state.interrupt_messages

    @pytest.mark.asyncio
    async def test_voice_activity_detection_no_audio(self, setup_test_environment):
        """Test voice activity detection with no audio data."""
        env = await setup_test_environment
        
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )
        
        # Test with no audio data
        input_data = {"audio_data": None}
        result = await interrupt_state.process(input_data)
        
        assert result.status == StatusType.SUCCESS
        assert result.outputs["interrupt_detected"] == False
        assert result.outputs["voice_activity"] == False
        assert result.meta["interrupt_state"] == "no_voice"

    @pytest.mark.asyncio
    async def test_voice_activity_detection_with_mock_audio(self, setup_test_environment):
        """Test voice activity detection with mock audio data."""
        env = await setup_test_environment
        
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )
        
        # Mock audio data (simulating voice activity)
        mock_audio_data = b"mock_audio_with_voice_activity"
        
        with patch.object(interrupt_state, '_detect_voice_activity', return_value=True):
            with patch.object(interrupt_state, '_confirm_interrupt_with_grace_period', return_value=True):
                input_data = {
                    "audio_data": mock_audio_data,
                    "current_tts_audio_path": "/test/audio.mp3",
                    "playback_position": 2.5
                }
                
                context = {
                    "workflow_state_config": {"reversible": True}
                }
                
                result = await interrupt_state.process(input_data, context)
                
                assert result.status == StatusType.SUCCESS
                assert result.outputs["interrupt_detected"] == True
                assert result.outputs["interrupt_confirmed"] == True
                assert result.outputs["voice_activity"] == True
                assert result.outputs["action_reversible"] == "reversible"
                assert "Allow me to finish this first" in result.outputs["acknowledgment_message"]

    @pytest.mark.asyncio
    async def test_action_reversibility_analysis_reversible(self, setup_test_environment):
        """Test action reversibility analysis for reversible actions."""
        env = await setup_test_environment
        
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )
        
        # Test reversible action
        context = {
            "workflow_state_config": {"reversible": True}
        }
        
        reversibility = interrupt_state._analyze_action_reversibility(context)
        assert reversibility == "reversible"

    @pytest.mark.asyncio
    async def test_action_reversibility_analysis_irreversible(self, setup_test_environment):
        """Test action reversibility analysis for irreversible actions."""
        env = await setup_test_environment
        
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )
        
        # Test irreversible action
        context = {
            "workflow_state_config": {"reversible": False}
        }
        
        reversibility = interrupt_state._analyze_action_reversibility(context)
        assert reversibility == "irreversible"

    @pytest.mark.asyncio
    async def test_action_reversibility_analysis_unknown(self, setup_test_environment):
        """Test action reversibility analysis for unknown actions."""
        env = await setup_test_environment
        
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )
        
        # Test unknown action (no context)
        reversibility = interrupt_state._analyze_action_reversibility(None)
        assert reversibility == "unknown"
        
        # Test unknown action (empty context)
        reversibility = interrupt_state._analyze_action_reversibility({})
        assert reversibility == "unknown"

    @pytest.mark.asyncio
    async def test_interrupt_flow_reversible_action(self, setup_test_environment):
        """Test complete interrupt flow for reversible action."""
        env = await setup_test_environment
        
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )
        
        # Mock voice detection and confirmation
        with patch.object(interrupt_state, '_detect_voice_activity', return_value=True):
            with patch.object(interrupt_state, '_confirm_interrupt_with_grace_period', return_value=True):
                with patch.object(interrupt_state, '_store_interrupt_context', return_value=None):
                    
                    input_data = {
                        "audio_data": b"mock_audio",
                        "current_tts_audio_path": "/test/audio.mp3",
                        "playback_position": 1.5,
                        "user_input": "Stop, I want to check something else"
                    }
                    
                    context = {
                        "workflow_state_config": {"reversible": True},
                        "memory_manager": env["state_manager"].memory_manager
                    }
                    
                    result = await interrupt_state.process(input_data, context)
                    
                    # Verify interrupt detection
                    assert result.status == StatusType.SUCCESS
                    assert result.outputs["interrupt_detected"] == True
                    assert result.outputs["interrupt_confirmed"] == True
                    assert result.outputs["action_reversible"] == "reversible"
                    
                    # Verify acknowledgment message
                    assert "Allow me to finish this first" in result.outputs["acknowledgment_message"]
                    
                    # Verify flow control
                    assert result.outputs["should_resume_tts"] == True
                    assert result.outputs["should_queue_input"] == True
                    assert result.outputs["playback_position"] == 1.5

    @pytest.mark.asyncio
    async def test_interrupt_flow_irreversible_action(self, setup_test_environment):
        """Test complete interrupt flow for irreversible action."""
        env = await setup_test_environment
        
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )
        
        # Mock voice detection and confirmation
        with patch.object(interrupt_state, '_detect_voice_activity', return_value=True):
            with patch.object(interrupt_state, '_confirm_interrupt_with_grace_period', return_value=True):
                with patch.object(interrupt_state, '_store_interrupt_context', return_value=None):
                    
                    input_data = {
                        "audio_data": b"mock_audio",
                        "current_tts_audio_path": "/test/audio.mp3",
                        "playback_position": 3.0,
                        "user_input": "Wait, cancel that transfer!"
                    }
                    
                    context = {
                        "workflow_state_config": {"reversible": False},
                        "memory_manager": env["state_manager"].memory_manager
                    }
                    
                    result = await interrupt_state.process(input_data, context)
                    
                    # Verify interrupt detection
                    assert result.status == StatusType.SUCCESS
                    assert result.outputs["interrupt_detected"] == True
                    assert result.outputs["interrupt_confirmed"] == True
                    assert result.outputs["action_reversible"] == "irreversible"
                    
                    # Verify acknowledgment message
                    assert "action has already been completed" in result.outputs["acknowledgment_message"]
                    
                    # Verify flow control
                    assert result.outputs["should_resume_tts"] == False
                    assert result.outputs["should_queue_input"] == False
                    assert result.outputs["playback_position"] == 3.0

    @pytest.mark.asyncio
    async def test_state_manager_tts_interrupt_coordination(self, setup_test_environment):
        """Test StateManager coordination with TTSState and InterruptState."""
        env = await setup_test_environment
        state_manager = env["state_manager"]

        # Mock TTS result
        mock_tts_result = StateOutput(
            status=StatusType.SUCCESS,
            message="TTS completed",
            code=StatusCode.OK,
            outputs={"audio_path": "/test/tts_audio.mp3", "latencyTTS": 1.2},
            meta={"tts_state": "completed"}
        )

        # Test TTS interrupt monitoring setup
        await state_manager._start_tts_interrupt_monitoring(mock_tts_result)

        # Verify TTS playback state was stored
        tts_state = await state_manager.memory_manager.get_tts_playback_state()
        assert tts_state is not None
        assert tts_state.get("audio_path") == "/test/tts_audio.mp3"
        assert tts_state.get("status") == "playing"

    @pytest.mark.asyncio
    async def test_state_manager_interrupt_detection_processing(self, setup_test_environment):
        """Test StateManager interrupt detection processing."""
        env = await setup_test_environment
        state_manager = env["state_manager"]

        # Mock audio data
        mock_audio_data = b"mock_interrupt_audio"

        # Test interrupt detection processing
        result = await state_manager.process_interrupt_detection(
            audio_data=mock_audio_data,
            current_tts_audio_path="/test/current_audio.mp3",
            playback_position=2.0
        )

        # Verify result structure
        assert result is not None
        assert hasattr(result, 'status')
        assert hasattr(result, 'outputs')

    @pytest.mark.asyncio
    async def test_interrupt_false_alarm_handling(self, setup_test_environment):
        """Test handling of false alarm interrupts."""
        env = await setup_test_environment

        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )

        # Mock voice detection but no confirmation (false alarm)
        with patch.object(interrupt_state, '_detect_voice_activity', return_value=True):
            with patch.object(interrupt_state, '_confirm_interrupt_with_grace_period', return_value=False):

                input_data = {
                    "audio_data": b"mock_audio_false_alarm",
                    "current_tts_audio_path": "/test/audio.mp3",
                    "playback_position": 1.0
                }

                result = await interrupt_state.process(input_data)

                assert result.status == StatusType.SUCCESS
                assert result.outputs["interrupt_detected"] == False
                assert result.outputs["voice_activity"] == True
                assert result.outputs["confirmed"] == False
                assert result.meta["interrupt_state"] == "false_alarm"

    @pytest.mark.asyncio
    async def test_interrupt_error_handling(self, setup_test_environment):
        """Test error handling in interrupt processing."""
        env = await setup_test_environment

        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )

        # Mock an exception during voice detection
        with patch.object(interrupt_state, '_detect_voice_activity', side_effect=Exception("VAD Error")):

            input_data = {
                "audio_data": b"mock_audio",
                "current_tts_audio_path": "/test/audio.mp3",
                "playback_position": 1.0
            }

            result = await interrupt_state.process(input_data)

            assert result.status == StatusType.ERROR
            assert "Interrupt processing error" in result.message
            assert "VAD Error" in result.meta["error"]

    @pytest.mark.asyncio
    async def test_memory_context_storage_and_retrieval(self, setup_test_environment):
        """Test interrupt context storage and retrieval from memory."""
        env = await setup_test_environment

        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=env["state_manager"].agent_registry,
            session_id=env["session_id"],
            interrupt_config=env["interrupt_config"]
        )

        # Test memory manager access
        memory_manager = await interrupt_state._get_memory_manager({
            "memory_manager": env["state_manager"].memory_manager
        })

        assert memory_manager is not None

        # Test interrupt context storage
        input_data = {
            "current_tts_audio_path": "/test/audio.mp3",
            "playback_position": 2.5,
            "user_input": "Test interrupt input"
        }

        await interrupt_state._store_interrupt_context(
            input_data, "reversible", "Test acknowledgment",
            {"memory_manager": memory_manager}
        )

        # Verify context was stored
        interrupt_context = await memory_manager.get_interrupt_context()
        assert interrupt_context is not None
        assert interrupt_context.get("detected") == True
        assert interrupt_context.get("confirmed") == True

    @pytest.mark.asyncio
    async def test_end_to_end_interrupt_workflow(self, setup_test_environment):
        """Test complete end-to-end interrupt workflow."""
        env = await setup_test_environment
        state_manager = env["state_manager"]

        # Step 1: Simulate TTS state execution
        mock_tts_result = StateOutput(
            status=StatusType.SUCCESS,
            message="TTS completed",
            code=StatusCode.OK,
            outputs={"audio_path": "/test/workflow_audio.mp3", "latencyTTS": 1.5},
            meta={"tts_state": "completed"}
        )

        # Step 2: Start TTS interrupt monitoring
        await state_manager._start_tts_interrupt_monitoring(mock_tts_result)

        # Step 3: Simulate interrupt detection
        mock_audio_data = b"user_interrupt_audio"
        interrupt_result = await state_manager.process_interrupt_detection(
            audio_data=mock_audio_data,
            current_tts_audio_path="/test/workflow_audio.mp3",
            playback_position=3.0
        )

        # Step 4: Verify interrupt was processed
        assert interrupt_result is not None

        # Step 5: Check memory state
        tts_state = await state_manager.memory_manager.get_tts_playback_state()
        assert tts_state is not None

        # Step 6: Verify StateManager interrupt monitoring
        await state_manager._monitor_and_handle_interrupts()


if __name__ == "__main__":
    # Run specific test
    async def run_single_test():
        test_instance = TestInterruptHandlingSystem()
        
        # Setup environment
        async for env in test_instance.setup_test_environment():
            # Run a specific test
            await test_instance.test_interrupt_flow_reversible_action(env)
            print("✅ Reversible action interrupt test passed")
            
            await test_instance.test_interrupt_flow_irreversible_action(env)
            print("✅ Irreversible action interrupt test passed")
            
            break
    
    asyncio.run(run_single_test())

"""
Audio utilities for the Voice Agents Platform.

This module provides audio processing utilities including format conversion,
voice activity detection, audio quality analysis, and audio preprocessing
with integrated structured logging.
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import io
import wave
import struct
import math
import os
import asyncio
import threading
import time
from datetime import datetime
from elevenlabs.client import ElevenLabs
import numpy as np
#import webrtcvad
from scipy.signal import butter, lfilter

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.config.interrupt_config import get_interrupt_config
interrupt_config = get_interrupt_config()

# Module logger
logger = get_module_logger("audio_utils")


class AudioProcessor:
    """Audio processing utilities with logging."""
    
    def __init__(self, interrupt_config=None):
        self.supported_formats = ['wav', 'mp3', 'flac', 'ogg']
        self.sample_rates = [8000, 16000, 22050, 44100, 48000]
        self.interrupt_config = interrupt_config
        logger.info(
            "Initialized AudioProcessor",
            action="initialize",
            output_data={
                "supported_formats": self.supported_formats,
                "supported_sample_rates": self.sample_rates
            },
            layer="audio_utils"
        )
    
    def analyze_audio_properties(self, audio_data: bytes, session_id: str = None) -> StateOutput:
        """
        Analyze audio properties like format, duration, sample rate, etc.
        
        Args:
            audio_data: Raw audio data
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with audio properties
        """
        try:
            logger.info(
                "Starting audio analysis",
                action="analyze_audio",
                input_data={"audio_size_bytes": len(audio_data)},
                layer="audio_utils",
                session_id=session_id
            )
            
            if not audio_data:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty audio data provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_audio"}
                )
            
            # Try to parse as WAV file
            try:
                audio_io = io.BytesIO(audio_data)
                with wave.open(audio_io, 'rb') as wav_file:
                    properties = {
                        "format": "wav",
                        "channels": wav_file.getnchannels(),
                        "sample_rate": wav_file.getframerate(),
                        "sample_width": wav_file.getsampwidth(),
                        "frames": wav_file.getnframes(),
                        "duration_seconds": wav_file.getnframes() / wav_file.getframerate(),
                        "size_bytes": len(audio_data)
                    }
                    
                    # Calculate additional metrics
                    properties["bitrate"] = properties["sample_rate"] * properties["sample_width"] * 8 * properties["channels"]
                    properties["is_stereo"] = properties["channels"] == 2
                    properties["is_mono"] = properties["channels"] == 1
                    
            except wave.Error:
                # If not WAV, provide basic analysis
                properties = {
                    "format": "unknown",
                    "size_bytes": len(audio_data),
                    "estimated_duration": len(audio_data) / 32000,  # Rough estimate
                    "analysis_note": "Could not parse as WAV file"
                }
            
            logger.info(
                "Audio analysis completed",
                action="analyze_audio",
                output_data=properties,
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Audio analysis completed",
                code=StatusCode.OK,
                outputs=properties,
                meta={"format": properties.get("format", "unknown")}
            )
            
        except Exception as e:
            logger.error(
                "Error in audio analysis",
                action="analyze_audio",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Audio analysis failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def bandpass_filter(self, data, sample_rate, lowcut=300.0, highcut=3400.0, order=5):
        """Apply a bandpass filter to focus on human speech frequencies."""
        nyq = 0.5 * sample_rate
        low = lowcut / nyq
        high = highcut / nyq
        b, a = butter(order, [low, high], btype='band')
        y = lfilter(b, a, data)
        return y

    def detect_voice_activity(self, audio_data: bytes, threshold: float = None, session_id: str = None) -> StateOutput:
        """
        Detect voice activity in audio data using webrtcvad if available, with bandpass filtering and robust format handling.
        Args:
            audio_data: Raw audio data
            threshold: Energy threshold for fallback voice detection
            session_id: Optional session ID for context
        Returns:
            StateOutput with voice activity detection results
        """
        if self.interrupt_config and not self.interrupt_config.global_settings.enabled:
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Interrupt detection is disabled in config.",
                code=StatusCode.OK,
                outputs={"has_voice": False, "detection_disabled": True},
                meta={"interrupt_detection_enabled": False}
            )
        if threshold is None and self.interrupt_config is not None:
            threshold = self.interrupt_config.global_settings.vad_threshold
        elif threshold is None:
            threshold = 0.01

        try:
            logger.info(
                "Starting voice activity detection (upgraded)",
                action="detect_voice_activity",
                input_data={"audio_size_bytes": len(audio_data), "threshold": threshold},
                layer="audio_utils",
                session_id=session_id
            )

            if not audio_data:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty audio data provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_audio"}
                )

            # Try to parse as WAV file
            try:
                audio_io = io.BytesIO(audio_data)
                with wave.open(audio_io, 'rb') as wav_file:
                    sample_rate = wav_file.getframerate()
                    sample_width = wav_file.getsampwidth()
                    channels = wav_file.getnchannels()
                    frames = wav_file.readframes(wav_file.getnframes())

                    # Convert to mono if needed
                    if channels > 1:
                        logger.info("Converting multi-channel audio to mono for VAD", action="detect_voice_activity", layer="audio_utils")
                        # Only keep the first channel
                        if sample_width == 2:
                            samples = np.frombuffer(frames, dtype=np.int16)
                            samples = samples[::channels]
                        elif sample_width == 1:
                            samples = np.frombuffer(frames, dtype=np.uint8)
                            samples = samples[::channels]
                        else:
                            raise ValueError(f"Unsupported sample width: {sample_width}")
                    else:
                        if sample_width == 2:
                            samples = np.frombuffer(frames, dtype=np.int16)
                        elif sample_width == 1:
                            samples = np.frombuffer(frames, dtype=np.uint8)
                        else:
                            raise ValueError(f"Unsupported sample width: {sample_width}")

                    # Apply bandpass filter
                    filtered_samples = self.bandpass_filter(samples, sample_rate)
                    filtered_samples = filtered_samples.astype(samples.dtype)

                    # Prepare PCM bytes for webrtcvad (16-bit mono PCM, 8kHz/16kHz/32kHz/48kHz)
                    if sample_width == 2:
                        pcm_bytes = filtered_samples.tobytes()
                    elif sample_width == 1:
                        # Convert 8-bit unsigned to 16-bit signed
                        pcm_bytes = (filtered_samples.astype(np.int16) - 128).tobytes()
                    else:
                        pcm_bytes = filtered_samples.tobytes()

                    # Use energy-based VAD (webrtcvad not available)
                    # Calculate energy-based voice detection
                    energy = np.mean(filtered_samples ** 2)
                    has_voice = energy > threshold
                    voice_ratio = 1.0 if has_voice else 0.0
                    num_windows = 1
                    voice_windows = 1 if has_voice else 0
                    logger.info(
                        "VAD result (energy-based)",
                        action="detect_voice_activity",
                        output_data={"has_voice": has_voice, "voice_ratio": voice_ratio, "num_windows": num_windows},
                        layer="audio_utils",
                        session_id=session_id
                    )
                    return StateOutput(
                        status=StatusType.SUCCESS,
                        message=f"Voice activity: {'detected' if has_voice else 'not detected'} (energy-based)",
                        code=StatusCode.OK,
                        outputs={
                            "has_voice": has_voice,
                            "voice_ratio": voice_ratio,
                            "total_windows": num_windows,
                            "voice_windows": voice_windows,
                            "vad_method": "energy"
                        },
                        meta={"has_voice": has_voice}
                    )
            except Exception as e:
                logger.warning(
                    "WAV parse or webrtcvad failed, falling back to energy-based VAD",
                    action="detect_voice_activity",
                    reason=str(e),
                    layer="audio_utils",
                    session_id=session_id
                )
                # Fallback: simple amplitude/energy analysis
                try:
                    # Try to decode as PCM 16-bit mono
                    samples = np.frombuffer(audio_data, dtype=np.int16)
                    sample_rate = 16000  # Assume 16kHz if unknown
                    filtered_samples = self.bandpass_filter(samples, sample_rate)
                    energy = np.mean(filtered_samples ** 2)
                    has_voice = energy > threshold
                    logger.info(
                        "Fallback VAD result (energy-based)",
                        action="detect_voice_activity",
                        output_data={"has_voice": has_voice, "energy": float(energy)},
                        layer="audio_utils",
                        session_id=session_id
                    )
                    return StateOutput(
                        status=StatusType.SUCCESS,
                        message=f"Voice activity: {'detected' if has_voice else 'not detected'} (energy-based)",
                        code=StatusCode.OK,
                        outputs={
                            "has_voice": has_voice,
                            "energy": float(energy),
                            "vad_method": "energy"
                        },
                        meta={"has_voice": has_voice}
                    )
                except Exception as e2:
                    logger.error(
                        "VAD failed on all methods",
                        action="detect_voice_activity",
                        reason=f"{str(e)} | {str(e2)}",
                        layer="audio_utils",
                        session_id=session_id
                    )
                    return StateOutput(
                        status=StatusType.ERROR,
                        message=f"Voice activity detection failed: {str(e)} | {str(e2)}",
                        code=StatusCode.INTERNAL_ERROR,
                        outputs={},
                        meta={"error": f"{str(e)} | {str(e2)}"}
                    )
        except Exception as e:
            logger.error(
                "Error in voice activity detection (upgraded)",
                action="detect_voice_activity",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Voice activity detection failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def validate_audio_quality(self, audio_data: bytes, session_id: str = None) -> StateOutput:
        """
        Validate audio quality and provide recommendations.
        
        Args:
            audio_data: Raw audio data
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with quality assessment
        """
        try:
            logger.info(
                "Starting audio quality validation",
                action="validate_quality",
                input_data={"audio_size_bytes": len(audio_data)},
                layer="audio_utils",
                session_id=session_id
            )
            
            # Get audio properties first
            props_result = self.analyze_audio_properties(audio_data, session_id)
            if props_result.status != StatusType.SUCCESS:
                return props_result
            
            properties = props_result.outputs
            quality_issues = []
            recommendations = []
            quality_score = 100  # Start with perfect score
            
            # Check sample rate
            if properties.get("sample_rate", 0) < 16000:
                quality_issues.append("Low sample rate")
                recommendations.append("Use at least 16kHz sample rate for better quality")
                quality_score -= 20
            
            # Check duration
            duration = properties.get("duration_seconds", 0)
            if duration < 0.5:
                quality_issues.append("Audio too short")
                recommendations.append("Provide at least 0.5 seconds of audio")
                quality_score -= 30
            elif duration > 30:
                quality_issues.append("Audio very long")
                recommendations.append("Consider splitting long audio into smaller chunks")
                quality_score -= 10
            
            # Check channels
            if properties.get("channels", 0) > 2:
                quality_issues.append("Too many channels")
                recommendations.append("Use mono or stereo audio")
                quality_score -= 15
            
            # Check file size
            size_mb = properties.get("size_bytes", 0) / (1024 * 1024)
            if size_mb > 10:
                quality_issues.append("Large file size")
                recommendations.append("Consider compressing audio or reducing quality")
                quality_score -= 10
            
            quality_score = max(quality_score, 0)  # Don't go below 0
            
            result = {
                "quality_score": quality_score,
                "quality_grade": "A" if quality_score >= 90 else "B" if quality_score >= 70 else "C" if quality_score >= 50 else "D",
                "issues": quality_issues,
                "recommendations": recommendations,
                "properties": properties,
                "is_acceptable": quality_score >= 50
            }
            
            logger.info(
                "Audio quality validation completed",
                action="validate_quality",
                output_data={
                    "quality_score": quality_score,
                    "quality_grade": result["quality_grade"],
                    "issues_count": len(quality_issues)
                },
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Audio quality: {result['quality_grade']} (Score: {quality_score})",
                code=StatusCode.OK,
                outputs=result,
                meta={"quality_score": quality_score}
            )
            
        except Exception as e:
            logger.error(
                "Error in audio quality validation",
                action="validate_quality",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Audio quality validation failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )


class TTSPlaybackController:
    """
    Controls TTS audio playback with pause/resume functionality and interrupt detection.
    Manages playback state and provides hooks for interrupt handling.
    """

    def __init__(self, session_id: str, interrupt_config=None):
        self.session_id = session_id
        self.logger = get_module_logger(f"TTSPlaybackController", session_id=session_id)
        self.interrupt_config = interrupt_config

        # Playback state
        self.is_playing = False
        self.is_paused = False
        self.current_audio_path = None
        self.playback_start_time = None
        self.pause_time = None
        self.total_pause_duration = 0
        self.audio_duration = None

        # Interrupt detection
        self.interrupt_detected = False
        self.interrupt_callback = None
        if interrupt_config is not None:
            self.vad_threshold = interrupt_config.global_settings.vad_threshold
            self.confirmation_window = interrupt_config.global_settings.confirmation_window_seconds
        else:
            self.vad_threshold = 0.01
            self.confirmation_window = 0.5

        # Threading for playback control
        self.playback_thread = None
        self.stop_playback = threading.Event()
        self.pause_playback_event = threading.Event()

    async def start_playback_with_interrupt_detection(self, audio_path: str, interrupt_callback=None, resume_from_position: float = 0.0) -> StateOutput:
        """
        Start audio playback with concurrent interrupt detection.

        Args:
            audio_path: Path to the audio file to play
            interrupt_callback: Async callback function to call when interrupt is detected
            resume_from_position: Position (in seconds) to start playback from (for partial resume)

        Returns:
            StateOutput with playback status
        """
        try:
            self.logger.info(
                "Starting TTS playback with interrupt detection",
                action="start_playback",
                input_data={"audio_path": audio_path, "resume_from_position": resume_from_position},
                layer="tts_playback_controller"
            )

            if not os.path.exists(audio_path):
                return StateOutput(
                    status=StatusType.ERROR,
                    message=f"Audio file not found: {audio_path}",
                    code=StatusCode.NOT_FOUND,
                    outputs={},
                    meta={"error": "file_not_found"}
                )

            # Get audio duration for tracking
            self.audio_duration = await self._get_audio_duration(audio_path)

            # Reset state
            self.current_audio_path = audio_path
            self.interrupt_callback = interrupt_callback
            self.interrupt_detected = False
            self.is_playing = True
            self.is_paused = False
            self.playback_start_time = time.time() - resume_from_position
            self.total_pause_duration = 0
            self.stop_playback.clear()
            self.pause_playback_event.clear()

            # Start playback in separate thread, pass resume_from_position
            self.playback_thread = threading.Thread(
                target=self._playback_worker,
                args=(audio_path, resume_from_position),
                daemon=True
            )
            self.playback_thread.start()

            # Start interrupt detection
            interrupt_task = asyncio.create_task(self._interrupt_detection_loop())

            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback started with interrupt detection",
                code=StatusCode.OK,
                outputs={
                    "playback_started": True,
                    "audio_path": audio_path,
                    "audio_duration": self.audio_duration,
                    "interrupt_detection_active": True,
                    "resume_from_position": resume_from_position
                },
                meta={"playback_controller": "active"}
            )

        except Exception as e:
            self.logger.error(
                "Error starting TTS playback",
                action="start_playback",
                input_data={"audio_path": audio_path, "resume_from_position": resume_from_position},
                reason=str(e),
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to start TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def pause_playback(self) -> StateOutput:
        """Pause the current audio playback."""
        try:
            if not self.is_playing or self.is_paused:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No active playback to pause",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "no_active_playback"}
                )

            self.pause_playback_event.set()
            self.is_paused = True
            self.pause_time = time.time()

            self.logger.info(
                "TTS playback paused",
                action="pause_playback",
                output_data={"paused_at": self.get_current_playback_position()},
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback paused",
                code=StatusCode.OK,
                outputs={
                    "paused": True,
                    "playback_position": self.get_current_playback_position()
                },
                meta={"playback_state": "paused"}
            )

        except Exception as e:
            self.logger.error(
                "Error pausing TTS playback",
                action="pause_playback",
                reason=str(e),
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to pause TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def resume_playback(self) -> StateOutput:
        """Resume paused audio playback."""
        try:
            if not self.is_paused:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No paused playback to resume",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "no_paused_playback"}
                )

            # Calculate pause duration
            if self.pause_time:
                self.total_pause_duration += time.time() - self.pause_time
                self.pause_time = None

            self.pause_playback_event.clear()
            self.is_paused = False

            self.logger.info(
                "TTS playback resumed",
                action="resume_playback",
                output_data={"resumed_at": self.get_current_playback_position()},
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback resumed",
                code=StatusCode.OK,
                outputs={
                    "resumed": True,
                    "playback_position": self.get_current_playback_position()
                },
                meta={"playback_state": "playing"}
            )

        except Exception as e:
            self.logger.error(
                "Error resuming TTS playback",
                action="resume_playback",
                reason=str(e),
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to resume TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def resume_playback_from_position(self, audio_path: str, position: float) -> StateOutput:
        """
        Resume TTS playback from a specific position.

        Args:
            audio_path: Path to the audio file to resume
            position: Position (in seconds) to resume from

        Returns:
            StateOutput with resume status
        """
        try:
            self.logger.info(
                "Resuming TTS playback from specific position",
                action="resume_playback_from_position",
                input_data={"audio_path": audio_path, "position": position},
                layer="tts_playback_controller"
            )

            # Stop current playback if any
            await self.stop_playback()

            # Start playback from the specified position
            return await self.start_playback_with_interrupt_detection(
                audio_path,
                interrupt_callback=None,  # No interrupt detection for resumed playback
                resume_from_position=position
            )

        except Exception as e:
            self.logger.error(
                "Error resuming TTS playback from position",
                action="resume_playback_from_position",
                reason=str(e),
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to resume TTS playback from position: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def stop_playback_completely(self) -> StateOutput:
        """Stop the current audio playback completely."""
        try:
            self.stop_playback.set()
            self.is_playing = False
            self.is_paused = False

            if self.playback_thread and self.playback_thread.is_alive():
                self.playback_thread.join(timeout=2.0)

            self.logger.info(
                "TTS playback stopped",
                action="stop_playback",
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback stopped",
                code=StatusCode.OK,
                outputs={"stopped": True},
                meta={"playback_state": "stopped"}
            )

        except Exception as e:
            self.logger.error(
                "Error stopping TTS playback",
                action="stop_playback",
                reason=str(e),
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to stop TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    def get_current_playback_position(self) -> float:
        """Get current playback position in seconds."""
        if not self.playback_start_time:
            return 0.0

        elapsed = time.time() - self.playback_start_time - self.total_pause_duration
        if self.is_paused and self.pause_time:
            elapsed -= (time.time() - self.pause_time)

        return max(0.0, elapsed)

    def get_playback_status(self) -> Dict[str, Any]:
        """Get comprehensive playback status."""
        return {
            "is_playing": self.is_playing,
            "is_paused": self.is_paused,
            "current_position": self.get_current_playback_position(),
            "audio_duration": self.audio_duration,
            "audio_path": self.current_audio_path,
            "interrupt_detected": self.interrupt_detected,
            "progress_percentage": (self.get_current_playback_position() / self.audio_duration * 100) if self.audio_duration else 0
        }

    async def _get_audio_duration(self, audio_path: str) -> float:
        """Get audio file duration in seconds."""
        try:
            # For now, use a simple estimation based on file size
            # In a production system, you'd use a proper audio library like pydub
            file_size = os.path.getsize(audio_path)
            # Rough estimation: MP3 at 128kbps ≈ 16KB/second
            estimated_duration = file_size / 16000
            return max(1.0, estimated_duration)  # Minimum 1 second
        except Exception:
            return 5.0  # Default fallback duration

    def _playback_worker(self, audio_path: str, resume_from_position: float = 0.0):
        """Worker thread for audio playback simulation with partial resume support."""
        try:
            # This is a simplified playback simulation
            # In production, you'd use pygame, pydub, or similar for actual audio playback

            duration = self.audio_duration or 5.0
            chunk_size = 0.1  # 100ms chunks
            total_chunks = int(duration / chunk_size)
            # Calculate starting chunk based on resume_from_position
            start_chunk = int(resume_from_position / chunk_size)
            chunks_played = start_chunk

            while chunks_played < total_chunks and not self.stop_playback.is_set():
                # Check for pause
                if self.pause_playback_event.is_set():
                    while self.pause_playback_event.is_set() and not self.stop_playback.is_set():
                        time.sleep(0.1)
                    continue

                # Simulate playing a chunk
                time.sleep(chunk_size)
                chunks_played += 1

            # Playback completed
            self.is_playing = False
            self.is_paused = False

        except Exception as e:
            self.logger.error(
                "Error in playback worker",
                action="_playback_worker",
                reason=str(e),
                layer="tts_playback_controller"
            )

    async def _interrupt_detection_loop(self):
        """Simulate interrupt detection for demo/testing (no VAD, no audio input)."""
        if self.interrupt_config and not self.interrupt_config.global_settings.enabled:
            self.logger.info(
                "Interrupt detection is disabled in config.",
                action="_interrupt_detection_loop",
                layer="tts_playback_controller"
            )
            return
        try:
            # Simulate waiting for a user interrupt after 1 second
            await asyncio.sleep(1.0)
            if self.is_playing and not self.stop_playback.is_set():
                await self._handle_interrupt()
        except Exception as e:
            self.logger.error(
                "Error in interrupt detection loop",
                action="_interrupt_detection_loop",
                reason=str(e),
                layer="tts_playback_controller"
            )

    def _should_check_for_interrupt(self) -> bool:
        """Determine if we should check for interrupts (avoid checking too frequently)."""
        # Only check if we've been playing for at least 0.5 seconds
        return self.get_current_playback_position() > 0.5

    async def _detect_interrupt(self) -> bool:
        """Deprecated: now handled in _interrupt_detection_loop with real VAD."""
        return False

    async def _handle_interrupt(self):
        """Handle detected interrupt."""
        try:
            self.interrupt_detected = True

            # Pause playback
            await self.pause_playback()

            # Call interrupt callback if provided
            if self.interrupt_callback:
                await self.interrupt_callback({
                    "session_id": self.session_id,
                    "playback_position": self.get_current_playback_position(),
                    "audio_path": self.current_audio_path,
                    "timestamp": datetime.now().isoformat()
                })

            self.logger.info(
                "Interrupt detected and handled",
                action="_handle_interrupt",
                output_data={
                    "playback_position": self.get_current_playback_position(),
                    "audio_duration": self.audio_duration
                },
                layer="tts_playback_controller"
            )

        except Exception as e:
            self.logger.error(
                "Error handling interrupt",
                action="_handle_interrupt",
                reason=str(e),
                layer="tts_playback_controller"
            )


# Convenience functions for easy access
def analyze_audio(audio_data: bytes, session_id: str = None) -> StateOutput:
    """Convenience function for audio analysis."""
    processor = AudioProcessor(interrupt_config=interrupt_config)
    return processor.analyze_audio_properties(audio_data, session_id)


def detect_voice_activity(audio_data: bytes, threshold: float = None, session_id: str = None) -> StateOutput:
    """Convenience function for voice activity detection."""
    processor = AudioProcessor(interrupt_config=interrupt_config)
    return processor.detect_voice_activity(audio_data, threshold, session_id)


def validate_audio_quality(audio_data: bytes, session_id: str = None) -> StateOutput:
    """Convenience function for audio quality validation."""
    processor = AudioProcessor(interrupt_config=interrupt_config)
    return processor.validate_audio_quality(audio_data, session_id)


# def synthesize_fallback_audio(text, session_id="fallback"):
#     try:
#         api_key = os.getenv("ELEVENLABS_API_KEY")
#         client = ElevenLabs(api_key=api_key)
#         voice_id = "EXAVITQu4vr4xnSDxMaL"  # Default voice
#         audio_path = os.path.abspath(f"tts_fallback_{session_id}.mp3")
#         audio_gen = client.text_to_speech.convert(
#             text=text,
#             voice_id=voice_id,
#             model_id="eleven_multilingual_v2",
#             output_format="mp3_44100_128"
#         )
#         with open(audio_path, "wb") as f:
#             for chunk in audio_gen:
#                 f.write(chunk)
#         return audio_path
#     except Exception as e:
#         print(f"[Fallback TTS] Could not generate fallback audio: {e}")
#         return None


#fallback using google TTS PROVIDER
def synthesize_fallback_audio(text, session_id="fallback"):
    try:
        from google.cloud import texttospeech
        import os

        # Create client
        client = texttospeech.TextToSpeechClient()

        # Set input text
        synthesis_input = texttospeech.SynthesisInput(text=text)

        # Use default fallback voice (female, en-US)
        voice = texttospeech.VoiceSelectionParams(
            language_code="en-US",
            ssml_gender=texttospeech.SsmlVoiceGender.FEMALE
        )

        # Select audio format
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3
        )

        # Generate speech
        response = client.synthesize_speech(
            input=synthesis_input, voice=voice, audio_config=audio_config
        )

        # Save to file
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
        audio_path = os.path.join(project_root, f"tts_fallback_{session_id}.mp3")

        with open(audio_path, "wb") as out:
            out.write(response.audio_content)

        return audio_path
    except Exception as e:
        print(f"[Fallback TTS] Could not generate fallback audio: {e}")
        return None

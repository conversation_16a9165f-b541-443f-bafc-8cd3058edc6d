"""
Comprehensive Integration Test: StateManager with Interrupt Handling

This test suite validates the complete integration between StateManager, TTSState, 
and InterruptState for real-world interrupt handling scenarios.

Test Coverage:
- StateManager workflow execution with interrupt monitoring
- TTSState and InterruptState coordination
- Real-time interrupt detection during TTS playback
- Action reversibility scenarios (reversible, irreversible, unknown)
- Memory context storage and retrieval across state transitions
- Complete interrupt workflow: detection → confirmation → acknowledgment → resume/queue
- Error handling and edge cases
"""

import sys
import asyncio
import os
import time
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from dotenv import load_dotenv

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

load_dotenv()

from core.state_manager.state_manager import StateManager
from core.state_manager.state_output import TTSState, InterruptState
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.memory.redis_context import RedisClient
from core.memory.memory_manager import MemoryManager
from core.config.interrupt_config import InterruptConfig, GlobalInterruptSettings
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.layer2_schema import TTSInputSchema, TTSOutputSchema

logger = get_module_logger("test_state_manager_with_interrupts", session_id="test_interrupt_integration")


class StateManagerInterruptIntegrationTest:
    """Comprehensive integration test for StateManager with interrupt handling."""
    
    def __init__(self):
        self.session_id = "integration_test_session"
        self.user_id = "integration_test_user"
        self.state_manager = None
        self.interrupt_config = None
    
    async def setup(self):
        """Setup test environment."""
        print("🔧 Setting up StateManager with interrupt handling...")
        
        # Setup logging
        setup_development_logging()
        
        # Create StateManager
        self.state_manager = await StateManager.create("banking_workflow.json", self.session_id, self.user_id)
        
        # Create interrupt configuration
        self.interrupt_config = InterruptConfig(
            global_settings=GlobalInterruptSettings(
                enabled=True,
                vad_threshold=0.01,
                confirmation_window_seconds=0.5,
                min_interrupt_duration_seconds=0.3
            )
        )
        
        print("✅ Setup completed")
    
    async def cleanup(self):
        """Cleanup test environment."""
        try:
            if self.state_manager:
                await self.state_manager.stop_interrupt_monitoring()
            cleanup_logger()
            print("✅ Cleanup completed")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")
    
    async def test_basic_state_manager_functionality(self):
        """Test basic StateManager functionality."""
        print("\n📋 Testing basic StateManager functionality...")
        
        # Test workflow retrieval
        workflow = await self.state_manager.getWorkflow()
        assert workflow is not None, "Workflow should not be None"
        workflow_name = getattr(workflow.workflow, 'name', 'Unknown') if hasattr(workflow, 'workflow') else 'Unknown'
        print(f"✅ Workflow retrieved: {workflow_name}")
        
        # Test current state
        current_state = await self.state_manager.getCurrentWorkflowState()
        assert current_state is not None, "Current state should not be None"
        print(f"✅ Current state: {current_state}")
        
        # Test pipeline map
        pipelines = await self.state_manager.getFullPipelineMap()
        assert pipelines is not None, "Pipelines should not be None"
        assert "interrupt_process" in self.state_manager.pipeline_state_map, "InterruptState should be in pipeline map"
        print("✅ Pipeline map includes InterruptState")
    
    async def test_tts_state_execution(self):
        """Test TTSState execution without interrupts."""
        print("\n🎵 Testing TTSState execution...")
        
        # Execute TTS state
        tts_input = {"text": "Hello, this is a test message for TTS processing."}
        
        # Mock TTS agent to avoid actual audio generation
        original_agent = self.state_manager.agent_registry.getAgent("tts_agent")
        mock_tts_agent = Mock()
        mock_tts_agent.process = AsyncMock(return_value=StateOutput(
            status=StatusType.SUCCESS,
            message="TTS completed",
            code=StatusCode.OK,
            outputs={"audio_path": "/test/mock_audio.mp3", "latencyTTS": 1.2},
            meta={"tts_state": "completed"}
        ))
        
        # Replace agent temporarily
        self.state_manager.agent_registry.agentObjects["tts_agent"] = mock_tts_agent
        
        try:
            # Execute TTS
            result = await self.state_manager.execute_step(tts_input)
            
            assert result.status == StatusType.SUCCESS, f"TTS execution should succeed, got {result.status}"
            assert "audio_path" in result.outputs, "TTS result should contain audio_path"
            print(f"✅ TTS execution successful: {result.outputs.get('audio_path')}")
            
            # Verify TTS playback state was stored
            tts_state = await self.state_manager.memory_manager.get_tts_playback_state()
            assert tts_state is not None, "TTS playback state should be stored"
            assert tts_state.get("status") == "playing", "TTS status should be 'playing'"
            print("✅ TTS playback state stored in memory")
            
        finally:
            # Restore original agent
            if original_agent:
                self.state_manager.agent_registry.agents["tts_agent"] = original_agent
    
    async def test_interrupt_state_creation_and_functionality(self):
        """Test InterruptState creation and basic functionality."""
        print("\n🔍 Testing InterruptState functionality...")
        
        # Create InterruptState
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=self.state_manager.agent_registry,
            session_id=self.session_id,
            interrupt_config=self.interrupt_config
        )
        
        assert interrupt_state is not None, "InterruptState should be created"
        assert interrupt_state.vad_threshold == 0.01, "VAD threshold should be configured"
        print("✅ InterruptState created with correct configuration")
        
        # Test voice activity detection with no audio
        input_data = {"audio_data": None}
        result = await interrupt_state.process(input_data)
        
        assert result.status == StatusType.SUCCESS, "No audio processing should succeed"
        assert result.outputs["interrupt_detected"] == False, "No interrupt should be detected"
        assert result.outputs["voice_activity"] == False, "No voice activity should be detected"
        print("✅ No audio detection works correctly")
    
    async def test_interrupt_detection_with_mock_audio(self):
        """Test interrupt detection with mock audio data."""
        print("\n🎤 Testing interrupt detection with mock audio...")
        
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=self.state_manager.agent_registry,
            session_id=self.session_id,
            interrupt_config=self.interrupt_config
        )
        
        # Mock voice detection methods
        with patch.object(interrupt_state, '_detect_voice_activity', return_value=True):
            with patch.object(interrupt_state, '_confirm_interrupt_with_grace_period', return_value=True):
                with patch.object(interrupt_state, '_store_interrupt_context', return_value=None):
                    
                    input_data = {
                        "audio_data": b"mock_audio_with_voice",
                        "current_tts_audio_path": "/test/current_audio.mp3",
                        "playback_position": 2.5,
                        "user_input": "Stop, I want to ask something else"
                    }
                    
                    context = {
                        "workflow_state_config": {"reversible": True},
                        "memory_manager": self.state_manager.memory_manager
                    }
                    
                    result = await interrupt_state.process(input_data, context)
                    
                    assert result.status == StatusType.SUCCESS, "Interrupt processing should succeed"
                    assert result.outputs["interrupt_detected"] == True, "Interrupt should be detected"
                    assert result.outputs["interrupt_confirmed"] == True, "Interrupt should be confirmed"
                    assert result.outputs["action_reversible"] == "reversible", "Action should be reversible"
                    assert "Allow me to finish this first" in result.outputs["acknowledgment_message"]
                    print("✅ Mock interrupt detection works correctly")
    
    async def test_action_reversibility_scenarios(self):
        """Test all three action reversibility scenarios."""
        print("\n🔄 Testing action reversibility scenarios...")
        
        interrupt_state = InterruptState(
            state_id="test_interrupt",
            agent_registry=self.state_manager.agent_registry,
            session_id=self.session_id,
            interrupt_config=self.interrupt_config
        )
        
        # Test scenarios
        scenarios = [
            ({"workflow_state_config": {"reversible": True}}, "reversible", "Allow me to finish this first"),
            ({"workflow_state_config": {"reversible": False}}, "irreversible", "action has already been completed"),
            ({}, "unknown", "I understand you want to say something")
        ]
        
        for context, expected_reversibility, expected_message_part in scenarios:
            reversibility = interrupt_state._analyze_action_reversibility(context)
            assert reversibility == expected_reversibility, f"Expected {expected_reversibility}, got {reversibility}"
            
            message = interrupt_state.interrupt_messages.get(reversibility)
            assert expected_message_part in message, f"Expected message part '{expected_message_part}' in '{message}'"
            
            print(f"✅ {expected_reversibility.capitalize()} action scenario works correctly")
    
    async def test_state_manager_interrupt_coordination(self):
        """Test StateManager coordination with InterruptState."""
        print("\n🎛️ Testing StateManager interrupt coordination...")
        
        # Mock TTS result
        mock_tts_result = StateOutput(
            status=StatusType.SUCCESS,
            message="TTS completed",
            code=StatusCode.OK,
            outputs={"audio_path": "/test/coordination_audio.mp3", "latencyTTS": 1.5},
            meta={"tts_state": "completed"}
        )
        
        # Test interrupt monitoring setup
        await self.state_manager._start_tts_interrupt_monitoring(mock_tts_result)
        
        # Verify TTS playback state was stored
        tts_state = await self.state_manager.memory_manager.get_tts_playback_state()
        assert tts_state is not None, "TTS playback state should be stored"
        assert tts_state.get("audio_path") == "/test/coordination_audio.mp3"
        print("✅ StateManager interrupt monitoring setup works")
        
        # Test interrupt detection processing
        mock_audio_data = b"mock_user_interrupt_audio"
        
        # Mock the interrupt processing to avoid real audio processing
        with patch.object(self.state_manager, '_process_audio_chunk_for_interrupt', return_value=True):
            with patch.object(self.state_manager, '_handle_detected_interrupt') as mock_handle:
                mock_handle.return_value = StateOutput(
                    status=StatusType.SUCCESS,
                    message="Interrupt handled",
                    code=StatusCode.OK,
                    outputs={"interrupt_confirmed": True, "action_reversible": "reversible"},
                    meta={}
                )
                
                result = await self.state_manager.process_interrupt_detection(
                    audio_data=mock_audio_data,
                    current_tts_audio_path="/test/coordination_audio.mp3",
                    playback_position=2.0
                )
                
                assert result is not None, "Interrupt detection should return result"
                print("✅ StateManager interrupt detection processing works")
    
    async def test_memory_context_integration(self):
        """Test memory context storage and retrieval across state transitions."""
        print("\n💾 Testing memory context integration...")
        
        # Store interrupt context
        await self.state_manager.memory_manager.set_interrupt_context(
            detected=True,
            confirmed=True,
            user_input_queued="Test interrupt input",
            resume_after_acknowledgment=True,
            action_reversible=True,
            interrupt_timestamp="2024-01-01T12:00:00Z"
        )
        
        # Retrieve and verify
        interrupt_context = await self.state_manager.memory_manager.get_interrupt_context()
        assert interrupt_context is not None, "Interrupt context should be stored"
        assert interrupt_context.get("detected") == True, "Detected flag should be True"
        assert interrupt_context.get("user_input_queued") == "Test interrupt input"
        print("✅ Memory context storage and retrieval works")
        
        # Test interrupt event logging
        await self.state_manager.memory_manager.add_interrupt_event(
            event_type="test_interrupt",
            details={"test": "data", "timestamp": "2024-01-01T12:00:00Z"}
        )
        
        interrupt_history = await self.state_manager.memory_manager.get_interrupt_history()
        assert interrupt_history is not None, "Interrupt history should be available"
        print("✅ Interrupt event logging works")
    
    async def test_end_to_end_interrupt_workflow(self):
        """Test complete end-to-end interrupt workflow."""
        print("\n🔄 Testing end-to-end interrupt workflow...")
        
        # Step 1: Execute TTS state (mocked)
        mock_tts_agent = Mock()
        mock_tts_agent.process = AsyncMock(return_value=StateOutput(
            status=StatusType.SUCCESS,
            message="TTS completed",
            code=StatusCode.OK,
            outputs={"audio_path": "/test/e2e_audio.mp3", "latencyTTS": 1.0},
            meta={"tts_state": "completed"}
        ))
        
        original_agent = self.state_manager.agent_registry.getAgent("tts_agent")
        self.state_manager.agent_registry.agents["tts_agent"] = mock_tts_agent
        
        try:
            # Execute TTS
            tts_input = {"text": "This is an end-to-end test message."}
            tts_result = await self.state_manager.execute_step(tts_input)
            assert tts_result.status == StatusType.SUCCESS, "TTS should succeed"
            print("✅ Step 1: TTS execution completed")
            
            # Step 2: Simulate interrupt detection
            mock_audio_data = b"user_interrupt_audio_e2e"
            
            # Mock interrupt processing
            with patch.object(self.state_manager, '_run_tts_with_interrupt_monitoring') as mock_monitoring:
                mock_monitoring.return_value = None  # Simulate monitoring completion
                
                interrupt_result = await self.state_manager.process_interrupt_detection(
                    audio_data=mock_audio_data,
                    current_tts_audio_path="/test/e2e_audio.mp3",
                    playback_position=1.5
                )
                
                assert interrupt_result is not None, "Interrupt processing should return result"
                print("✅ Step 2: Interrupt detection completed")
            
            # Step 3: Verify memory state
            tts_state = await self.state_manager.memory_manager.get_tts_playback_state()
            assert tts_state is not None, "TTS state should be in memory"
            print("✅ Step 3: Memory state verification completed")
            
            # Step 4: Test StateManager interrupt monitoring
            await self.state_manager._monitor_and_handle_interrupts()
            print("✅ Step 4: StateManager interrupt monitoring completed")
            
            print("🎉 End-to-end interrupt workflow test completed successfully!")
            
        finally:
            # Restore original agent
            if original_agent:
                self.state_manager.agent_registry.agents["tts_agent"] = original_agent
    
    async def run_all_tests(self):
        """Run all integration tests."""
        print("🚀 Starting StateManager with Interrupts Integration Tests")
        print("=" * 70)
        
        try:
            await self.setup()
            
            # Run all tests
            await self.test_basic_state_manager_functionality()
            await self.test_tts_state_execution()
            await self.test_interrupt_state_creation_and_functionality()
            await self.test_interrupt_detection_with_mock_audio()
            await self.test_action_reversibility_scenarios()
            await self.test_state_manager_interrupt_coordination()
            await self.test_memory_context_integration()
            await self.test_end_to_end_interrupt_workflow()
            
            print("\n🎉 All Integration Tests Passed Successfully!")
            print("=" * 70)
            
            # Summary
            print("\n📊 Test Summary:")
            print("✅ StateManager basic functionality")
            print("✅ TTSState execution and monitoring setup")
            print("✅ InterruptState creation and voice detection")
            print("✅ Interrupt detection with mock audio")
            print("✅ Action reversibility scenarios (reversible, irreversible, unknown)")
            print("✅ StateManager interrupt coordination")
            print("✅ Memory context storage and retrieval")
            print("✅ End-to-end interrupt workflow")
            
        except Exception as e:
            print(f"\n❌ Test failed: {str(e)}")
            raise
        finally:
            await self.cleanup()


async def main():
    """Main test runner."""
    test_suite = StateManagerInterruptIntegrationTest()
    await test_suite.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())

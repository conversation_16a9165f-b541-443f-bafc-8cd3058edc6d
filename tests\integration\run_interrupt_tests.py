"""
Interrupt Handling System Test Runner

This script demonstrates the interrupt handling system functionality
by running integration tests and showing the interaction between
StateManager, TTSState, and InterruptState.
"""

import sys
import asyncio
import os
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

load_dotenv()

from core.state_manager.state_manager import StateManager
from core.state_manager.state_output import TTSState, InterruptState
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.config.interrupt_config import InterruptConfig, InterruptDetectionConfig
from schemas.outputSchema import StateOutput, StatusType, StatusCode

logger = get_module_logger("interrupt_test_runner", session_id="demo_session")


async def demo_interrupt_handling_system():
    """
    Demonstrate the interrupt handling system with realistic scenarios.
    """
    print("🎯 Starting Interrupt Handling System Demo")
    print("=" * 60)
    
    # Setup logging
    setup_development_logging()
    
    try:
        # Initialize StateManager
        print("\n📋 1. Initializing StateManager...")
        session_id = "demo_interrupt_session"
        user_id = "demo_user"
        state_manager = await StateManager.create("banking_workflow.json", session_id, user_id)
        print(f"✅ StateManager initialized for session: {session_id}")
        
        # Create interrupt configuration
        print("\n⚙️ 2. Setting up interrupt configuration...")
        interrupt_config = InterruptConfig(
            detection=InterruptDetectionConfig(
                enabled=True,
                vad_threshold=0.01,
                confirmation_window_seconds=0.5,
                min_interrupt_duration_seconds=0.3
            )
        )
        print("✅ Interrupt configuration created")
        
        # Test 1: Create and test InterruptState
        print("\n🔍 3. Testing InterruptState creation and basic functionality...")
        interrupt_state = InterruptState(
            state_id="demo_interrupt",
            agent_registry=state_manager.agent_registry,
            session_id=session_id,
            interrupt_config=interrupt_config
        )
        print(f"✅ InterruptState created with VAD threshold: {interrupt_state.vad_threshold}")
        print(f"✅ Confirmation window: {interrupt_state.confirmation_window}s")
        
        # Test 2: Test voice activity detection with no audio
        print("\n🔇 4. Testing voice activity detection with no audio...")
        no_audio_result = await interrupt_state.process({"audio_data": None})
        print(f"✅ No audio result: {no_audio_result.outputs['interrupt_detected']}")
        print(f"   Voice activity: {no_audio_result.outputs['voice_activity']}")
        print(f"   State: {no_audio_result.meta['interrupt_state']}")
        
        # Test 3: Test action reversibility analysis
        print("\n🔄 5. Testing action reversibility analysis...")
        
        # Reversible action
        reversible_context = {"workflow_state_config": {"reversible": True}}
        reversibility = interrupt_state._analyze_action_reversibility(reversible_context)
        print(f"✅ Reversible action analysis: {reversibility}")
        
        # Irreversible action
        irreversible_context = {"workflow_state_config": {"reversible": False}}
        reversibility = interrupt_state._analyze_action_reversibility(irreversible_context)
        print(f"✅ Irreversible action analysis: {reversibility}")
        
        # Unknown action
        reversibility = interrupt_state._analyze_action_reversibility(None)
        print(f"✅ Unknown action analysis: {reversibility}")
        
        # Test 4: Test StateManager TTS interrupt coordination
        print("\n🎵 6. Testing StateManager TTS interrupt coordination...")
        mock_tts_result = StateOutput(
            status=StatusType.SUCCESS,
            message="TTS completed",
            code=StatusCode.OK,
            outputs={"audio_path": "/demo/tts_audio.mp3", "latencyTTS": 1.2},
            meta={"tts_state": "completed"}
        )
        
        await state_manager._start_tts_interrupt_monitoring(mock_tts_result)
        print("✅ TTS interrupt monitoring started")
        
        # Verify TTS playback state was stored
        tts_state = await state_manager.memory_manager.get_tts_playback_state()
        if tts_state:
            print(f"✅ TTS playback state stored: {tts_state.get('audio_path')}")
            print(f"   Status: {tts_state.get('status')}")
        
        # Test 5: Test interrupt detection processing
        print("\n🎤 7. Testing interrupt detection processing...")
        mock_audio_data = b"mock_user_interrupt_audio_data"
        
        interrupt_result = await state_manager.process_interrupt_detection(
            audio_data=mock_audio_data,
            current_tts_audio_path="/demo/current_audio.mp3",
            playback_position=2.5
        )
        
        print(f"✅ Interrupt detection processed")
        print(f"   Result status: {interrupt_result.status}")
        print(f"   Has outputs: {bool(interrupt_result.outputs)}")
        
        # Test 6: Demonstrate different interrupt scenarios
        print("\n📋 8. Demonstrating interrupt scenarios...")
        
        # Scenario 1: Reversible action interrupt
        print("\n   Scenario 1: Reversible Action Interrupt")
        reversible_input = {
            "audio_data": b"mock_audio_reversible",
            "current_tts_audio_path": "/demo/reversible_audio.mp3",
            "playback_position": 1.5,
            "user_input": "Wait, I want to check something else"
        }
        
        reversible_context = {
            "workflow_state_config": {"reversible": True},
            "memory_manager": state_manager.memory_manager
        }
        
        # Mock the voice detection methods for demo
        original_detect = interrupt_state._detect_voice_activity
        original_confirm = interrupt_state._confirm_interrupt_with_grace_period
        
        interrupt_state._detect_voice_activity = lambda x: True
        interrupt_state._confirm_interrupt_with_grace_period = lambda: True
        
        reversible_result = await interrupt_state.process(reversible_input, reversible_context)
        
        print(f"   ✅ Interrupt detected: {reversible_result.outputs['interrupt_detected']}")
        print(f"   ✅ Action reversible: {reversible_result.outputs['action_reversible']}")
        print(f"   ✅ Should resume TTS: {reversible_result.outputs['should_resume_tts']}")
        print(f"   ✅ Should queue input: {reversible_result.outputs['should_queue_input']}")
        print(f"   💬 Acknowledgment: {reversible_result.outputs['acknowledgment_message']}")
        
        # Scenario 2: Irreversible action interrupt
        print("\n   Scenario 2: Irreversible Action Interrupt")
        irreversible_input = {
            "audio_data": b"mock_audio_irreversible",
            "current_tts_audio_path": "/demo/irreversible_audio.mp3",
            "playback_position": 3.0,
            "user_input": "Stop! Cancel that transfer!"
        }
        
        irreversible_context = {
            "workflow_state_config": {"reversible": False},
            "memory_manager": state_manager.memory_manager
        }
        
        irreversible_result = await interrupt_state.process(irreversible_input, irreversible_context)
        
        print(f"   ✅ Interrupt detected: {irreversible_result.outputs['interrupt_detected']}")
        print(f"   ✅ Action reversible: {irreversible_result.outputs['action_reversible']}")
        print(f"   ✅ Should resume TTS: {irreversible_result.outputs['should_resume_tts']}")
        print(f"   ✅ Should queue input: {irreversible_result.outputs['should_queue_input']}")
        print(f"   💬 Acknowledgment: {irreversible_result.outputs['acknowledgment_message']}")
        
        # Restore original methods
        interrupt_state._detect_voice_activity = original_detect
        interrupt_state._confirm_interrupt_with_grace_period = original_confirm
        
        # Test 7: Test StateManager interrupt monitoring
        print("\n🔍 9. Testing StateManager interrupt monitoring...")
        await state_manager._monitor_and_handle_interrupts()
        print("✅ StateManager interrupt monitoring completed")
        
        print("\n🎉 Interrupt Handling System Demo Completed Successfully!")
        print("=" * 60)
        
        # Summary
        print("\n📊 Demo Summary:")
        print("✅ InterruptState creation and configuration")
        print("✅ Voice activity detection (with and without audio)")
        print("✅ Action reversibility analysis (reversible, irreversible, unknown)")
        print("✅ StateManager TTS interrupt coordination")
        print("✅ Interrupt detection processing")
        print("✅ Context-aware acknowledgment generation")
        print("✅ Memory context storage and retrieval")
        print("✅ Complete interrupt workflow scenarios")
        print("✅ StateManager interrupt monitoring")
        
        print("\n🔧 System Architecture:")
        print("📦 TTSState: Handles TTS audio generation")
        print("🔍 InterruptState: Dedicated interrupt detection and handling")
        print("🎛️ StateManager: Coordinates between states and manages workflow")
        print("💾 MemoryManager: Stores interrupt context for coordination")
        print("⚙️ InterruptConfig: Configurable VAD thresholds and timing")
        
    except Exception as e:
        print(f"\n❌ Error during demo: {str(e)}")
        logger.error(f"Demo error: {str(e)}")
        
    finally:
        # Cleanup
        cleanup_logger()


async def run_specific_test_scenario():
    """Run a specific test scenario for debugging."""
    print("🧪 Running Specific Test Scenario")
    print("-" * 40)
    
    setup_development_logging()
    
    try:
        # Quick test of InterruptState functionality
        session_id = "quick_test_session"
        user_id = "test_user"
        
        state_manager = await StateManager.create("banking_workflow.json", session_id, user_id)
        
        interrupt_config = InterruptConfig(
            detection=InterruptDetectionConfig(
                enabled=True,
                vad_threshold=0.01,
                confirmation_window_seconds=0.2,  # Faster for testing
                min_interrupt_duration_seconds=0.1
            )
        )
        
        interrupt_state = InterruptState(
            state_id="quick_test",
            agent_registry=state_manager.agent_registry,
            session_id=session_id,
            interrupt_config=interrupt_config
        )
        
        # Test reversibility analysis
        test_cases = [
            ({"workflow_state_config": {"reversible": True}}, "reversible"),
            ({"workflow_state_config": {"reversible": False}}, "irreversible"),
            ({}, "unknown"),
            (None, "unknown")
        ]
        
        for context, expected in test_cases:
            result = interrupt_state._analyze_action_reversibility(context)
            status = "✅" if result == expected else "❌"
            print(f"{status} Context: {context} → Result: {result} (Expected: {expected})")
        
        print("\n✅ Specific test scenario completed")
        
    except Exception as e:
        print(f"❌ Test scenario error: {str(e)}")
    
    finally:
        cleanup_logger()


if __name__ == "__main__":
    print("🚀 Interrupt Handling System Test Runner")
    print("Choose an option:")
    print("1. Run full demo")
    print("2. Run specific test scenario")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        asyncio.run(demo_interrupt_handling_system())
    elif choice == "2":
        asyncio.run(run_specific_test_scenario())
    else:
        print("Invalid choice. Running full demo...")
        asyncio.run(demo_interrupt_handling_system())
